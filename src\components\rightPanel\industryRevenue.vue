<!-- 监测类型 -->
<template>
  <CPanel>
    <template #header>
      <div class="header-content">
        <div class="header-line"></div>
        <span>监测类型</span>
        <span class="header-subtitle">TYPE OF MONITORING</span>
      </div>
    </template>
    <template #content>
      <div class="monitor-types">
        <div v-for="(item, index) in monitorData" :key="index" class="monitor-item">
          <div class="monitor-bar">
            <div class="bar-fill" :style="{ width: item.percentage + '%' }"></div>
          </div>
          <div class="monitor-info">
            <span class="monitor-name">{{ item.name }}</span>
            <span class="monitor-count">{{ item.count }}(个)</span>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup>
import { ref } from 'vue'
import CPanel from '@/components/common/CPanel.vue'

// 监测类型数据
const monitorData = ref([
  {
    name: '水质监测',
    count: 20,
    percentage: 65
  },
  {
    name: '水位监测',
    count: 15,
    percentage: 50
  },
  {
    name: '桥梁参流监测',
    count: 30,
    percentage: 85
  },
  {
    name: '桥梁变形监测',
    count: 30,
    percentage: 85
  },
  {
    name: '桥梁荷载监测',
    count: 25,
    percentage: 70
  }
])

</script>

<style lang="scss" scoped>
.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;

  .header-line {
    width: 4px;
    height: 20px;
    background: linear-gradient(180deg, #00D4FF 0%, #0099CC 100%);
    border-radius: 2px;
  }

  .header-subtitle {
    font-size: 12px;
    color: #fff;
    margin-left: 8px;
  }
}

.monitor-types {
  padding: 20px 0;
}

.monitor-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.monitor-bar {
  width: 120px;
  height: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  overflow: hidden;
  margin-right: 15px;
  position: relative;

  .bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #4A90E2 0%, #50E3C2 100%);
    border-radius: 8px;
    transition: width 0.8s ease;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 2px;
      height: 100%;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 0 8px 8px 0;
    }
  }
}

.monitor-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.monitor-name {
  color: #C5D6E6;
  font-size: 14px;
  font-weight: 400;
}

.monitor-count {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}
</style>
