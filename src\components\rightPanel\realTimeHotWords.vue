<!-- 桥隧病害处理 -->
<template>
  <CPanel>
    <template #header>
      <div class="header-content">
        <div class="header-line"></div>
        <span>桥隧病害处理</span>
        <span class="header-subtitle">BRIDGE AND TUNNEL TREAT</span>
      </div>
    </template>
    <template #content>
       <div class="controls-row">
          <div class="time-tabs">
            <span class="tab active">日</span>
            <span class="tab">月</span>
            <span class="tab">年</span>
          </div>
          <div class="legend">
            <div class="legend-item">
              <span class="legend-color untreated"></span>
              <span class="legend-text">未处理</span>
            </div>
            <div class="legend-item">
              <span class="legend-color treated"></span>
              <span class="legend-text">已处理</span>
            </div>
          </div>
        </div>
      <div class="chart-container">
        <CEcharts :option="option" />
      </div>
    </template>
  </CPanel>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'

const option = ref({})

const initEcharts = () => {
  // 桥隧病害处理数据
  const bridgeData = [
    { name: '戴铁路大桥', untreated: 1, treated: 3 },
    { name: '安庄大桥', untreated: 3, treated: 1 },
    { name: '官井南隧道', untreated: 2, treated: 4 }
  ]
  const options = {
    backgroundColor: 'transparent',
    grid: {
      left: '1%',
      right: '1%',
      top: '15%',
      bottom: '1%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: bridgeData.map(item => item.name),
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12,
        interval: 0,
        rotate: 0,
        margin: 15
      },
      splitLine: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      max: 6,
      interval: 1,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.2)',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '未处理',
        type: 'bar',
        stack: 'total',
        barWidth: 20,
        itemStyle: {
          color: '#00FF7F'
        },
        data: bridgeData.map(item => item.untreated)
      },
      {
        name: '已处理',
        type: 'bar',
        stack: 'total',
        barWidth: 20,
        itemStyle: {
          color: '#4169E1'
        },
        data: bridgeData.map(item => item.treated)
      }
    ]
  }
  return options
}
onMounted(() => {
  option.value = initEcharts()
})
</script>

<style lang="scss" scoped>
.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;

  .header-line {
    width: 4px;
    height: 20px;
    background: linear-gradient(180deg, #00D4FF 0%, #0099CC 100%);
    border-radius: 2px;
  }

  .header-subtitle {
    font-size: 12px;
    color: #fff;
    margin-left: 8px;
  }
}

.controls-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.time-tabs {
  display: flex;
  gap: 0;

  .tab {
    padding: 4px 12px;
    font-size: 12px;
    color: #fff;
    border: 1px solid #00D4FF;
    cursor: pointer;
    background: transparent;
    transition: all 0.3s ease;

    &:first-child {
      border-radius: 4px 0 0 4px;
    }

    &:last-child {
      border-radius: 0 4px 4px 0;
    }

    &:not(:first-child) {
      border-left: none;
    }

    &.active {
      background: #00D4FF;
      color: #000;
    }

    &:hover:not(.active) {
      background: rgba(0, 212, 255, 0.2);
    }
  }
}

.legend {
  display: flex;
  gap: 20px;

  .legend-item {
    display: flex;
    align-items: center;
    gap: 6px;

    .legend-color {
      width: 12px;
      height: 12px;
      border-radius: 2px;

      &.untreated {
        background: #00FF7F;
      }

      &.treated {
        background: #4169E1;
      }
    }

    .legend-text {
      font-size: 12px;
      color: #fff;
    }
  }
}

.chart-container {
  width: 100%;
  height: 300px;
}
</style>
